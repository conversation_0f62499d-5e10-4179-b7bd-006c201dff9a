"use client"

import React, { forwardRef } from "react"
import { useForm, UseFormReturn, FieldPath, FieldValues } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { AlertCircle, Eye, EyeOff } from "lucide-react"
import { LoadingButton } from "../LoadingStates"

// Form field wrapper with error handling
interface FormFieldProps {
  label: string
  error?: string
  required?: boolean
  children: React.ReactNode
  className?: string
}

export function FormField({ label, error, required, children, className = "" }: FormFieldProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-300">
        {label}
        {required && <span className="text-red-400 mr-1">*</span>}
      </label>
      {children}
      {error && (
        <div className="flex items-center gap-2 text-red-400 text-sm">
          <AlertCircle className="w-4 h-4" />
          <span>{error}</span>
        </div>
      )}
    </div>
  )
}

// Enhanced input component
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ className = "", error, ...props }, ref) => {
    return (
      <input
        ref={ref}
        className={`
          w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400
          focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50
          transition-all duration-200
          ${error ? 'border-red-500' : 'border-gray-700'}
          ${className}
        `}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

// Enhanced textarea component
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: string
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className = "", error, ...props }, ref) => {
    return (
      <textarea
        ref={ref}
        className={`
          w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400
          focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50
          transition-all duration-200 resize-vertical min-h-[100px]
          ${error ? 'border-red-500' : 'border-gray-700'}
          ${className}
        `}
        {...props}
      />
    )
  }
)
Textarea.displayName = "Textarea"

// Password input with toggle visibility
interface PasswordInputProps extends Omit<InputProps, 'type'> {
  showToggle?: boolean
}

export function PasswordInput({ showToggle = true, ...props }: PasswordInputProps) {
  const [showPassword, setShowPassword] = React.useState(false)

  return (
    <div className="relative">
      <Input
        {...props}
        type={showPassword ? "text" : "password"}
      />
      {showToggle && (
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
        >
          {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
        </button>
      )}
    </div>
  )
}

// Select component
interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  error?: string
  options: { value: string; label: string }[]
  placeholder?: string
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ className = "", error, options, placeholder, ...props }, ref) => {
    return (
      <select
        ref={ref}
        className={`
          w-full px-3 py-2 bg-gray-800 border rounded-lg text-white
          focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50
          transition-all duration-200
          ${error ? 'border-red-500' : 'border-gray-700'}
          ${className}
        `}
        {...props}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    )
  }
)
Select.displayName = "Select"

// Checkbox component
interface CheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string
  error?: string
}

export const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className = "", label, error, ...props }, ref) => {
    return (
      <div className="space-y-2">
        <label className="flex items-center gap-3 cursor-pointer">
          <input
            ref={ref}
            type="checkbox"
            className={`
              w-4 h-4 text-purple-600 bg-gray-800 border-gray-700 rounded
              focus:ring-purple-500/50 focus:ring-2
              ${error ? 'border-red-500' : ''}
              ${className}
            `}
            {...props}
          />
          <span className="text-sm text-gray-300">{label}</span>
        </label>
        {error && (
          <div className="flex items-center gap-2 text-red-400 text-sm">
            <AlertCircle className="w-4 h-4" />
            <span>{error}</span>
          </div>
        )}
      </div>
    )
  }
)
Checkbox.displayName = "Checkbox"

// Form wrapper with validation
interface FormProps<T extends FieldValues> {
  schema: z.ZodSchema<T>
  onSubmit: (data: T) => Promise<void> | void
  children: (form: UseFormReturn<T>) => React.ReactNode
  defaultValues?: Partial<T>
  className?: string
}

export function Form<T extends FieldValues>({
  schema,
  onSubmit,
  children,
  defaultValues,
  className = ""
}: FormProps<T>) {
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues,
  })

  const [isSubmitting, setIsSubmitting] = React.useState(false)

  const handleSubmit = async (data: T) => {
    try {
      setIsSubmitting(true)
      await onSubmit(data)
    } catch (error) {
      console.error('Form submission error:', error)
      // Handle error (show toast, etc.)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form
      onSubmit={form.handleSubmit(handleSubmit)}
      className={className}
      noValidate
    >
      {children({ ...form, isSubmitting })}
    </form>
  )
}

// Form field with react-hook-form integration
interface FormInputProps<T extends FieldValues> {
  form: UseFormReturn<T>
  name: FieldPath<T>
  label: string
  type?: string
  placeholder?: string
  required?: boolean
  className?: string
}

export function FormInput<T extends FieldValues>({
  form,
  name,
  label,
  type = "text",
  placeholder,
  required,
  className
}: FormInputProps<T>) {
  const error = form.formState.errors[name]?.message as string

  return (
    <FormField label={label} error={error} required={required} className={className}>
      <Input
        {...form.register(name)}
        type={type}
        placeholder={placeholder}
        error={error}
      />
    </FormField>
  )
}

// Form textarea with react-hook-form integration
interface FormTextareaProps<T extends FieldValues> {
  form: UseFormReturn<T>
  name: FieldPath<T>
  label: string
  placeholder?: string
  required?: boolean
  className?: string
}

export function FormTextarea<T extends FieldValues>({
  form,
  name,
  label,
  placeholder,
  required,
  className
}: FormTextareaProps<T>) {
  const error = form.formState.errors[name]?.message as string

  return (
    <FormField label={label} error={error} required={required} className={className}>
      <Textarea
        {...form.register(name)}
        placeholder={placeholder}
        error={error}
      />
    </FormField>
  )
}

// Form select with react-hook-form integration
interface FormSelectProps<T extends FieldValues> {
  form: UseFormReturn<T>
  name: FieldPath<T>
  label: string
  options: { value: string; label: string }[]
  placeholder?: string
  required?: boolean
  className?: string
}

export function FormSelect<T extends FieldValues>({
  form,
  name,
  label,
  options,
  placeholder,
  required,
  className
}: FormSelectProps<T>) {
  const error = form.formState.errors[name]?.message as string

  return (
    <FormField label={label} error={error} required={required} className={className}>
      <Select
        {...form.register(name)}
        options={options}
        placeholder={placeholder}
        error={error}
      />
    </FormField>
  )
}

// Submit button component
interface SubmitButtonProps {
  loading?: boolean
  children: React.ReactNode
  className?: string
}

export function SubmitButton({ loading, children, className = "" }: SubmitButtonProps) {
  return (
    <LoadingButton
      type="submit"
      loading={loading || false}
      className={`
        w-full bg-gradient-to-r from-purple-600 to-pink-600 
        hover:from-purple-700 hover:to-pink-700 
        text-white px-6 py-3 rounded-lg font-medium 
        transition-all duration-200 hover:shadow-lg
        disabled:opacity-50 disabled:cursor-not-allowed
        ${className}
      `}
    >
      {children}
    </LoadingButton>
  )
}
