"use client"

import { useEffect, useCallback, useRef } from 'react'

interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  interactionTime: number
  memoryUsage?: number
}

interface UsePerformanceOptions {
  trackMemory?: boolean
  trackInteractions?: boolean
  onMetrics?: (metrics: PerformanceMetrics) => void
}

export function usePerformance(
  componentName: string,
  options: UsePerformanceOptions = {}
) {
  const { trackMemory = false, trackInteractions = true, onMetrics } = options
  const startTimeRef = useRef<number>(Date.now())
  const renderStartRef = useRef<number>(Date.now())
  const metricsRef = useRef<Partial<PerformanceMetrics>>({})

  // Track component load time
  useEffect(() => {
    const loadTime = Date.now() - startTimeRef.current
    metricsRef.current.loadTime = loadTime

    // Track render time
    const renderTime = Date.now() - renderStartRef.current
    metricsRef.current.renderTime = renderTime

    // Track memory usage if supported and enabled
    if (trackMemory && 'memory' in performance) {
      const memory = (performance as any).memory
      metricsRef.current.memoryUsage = memory.usedJSHeapSize
    }

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚀 Performance Metrics: ${componentName}`)
      console.log(`Load Time: ${loadTime}ms`)
      console.log(`Render Time: ${renderTime}ms`)
      if (metricsRef.current.memoryUsage) {
        console.log(`Memory Usage: ${(metricsRef.current.memoryUsage / 1024 / 1024).toFixed(2)}MB`)
      }
      console.groupEnd()
    }

    // Call metrics callback if provided
    if (onMetrics && metricsRef.current.loadTime !== undefined) {
      onMetrics(metricsRef.current as PerformanceMetrics)
    }
  }, [componentName, trackMemory, onMetrics])

  // Track user interactions
  const trackInteraction = useCallback((interactionName: string) => {
    if (!trackInteractions) return

    const interactionTime = Date.now()
    metricsRef.current.interactionTime = interactionTime - startTimeRef.current

    if (process.env.NODE_ENV === 'development') {
      console.log(`⚡ Interaction "${interactionName}" at ${metricsRef.current.interactionTime}ms`)
    }
  }, [trackInteractions])

  // Measure async operations
  const measureAsync = useCallback(async <T>(
    operationName: string,
    operation: () => Promise<T>
  ): Promise<T> => {
    const start = performance.now()
    
    try {
      const result = await operation()
      const duration = performance.now() - start
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`⏱️ Async operation "${operationName}" took ${duration.toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      const duration = performance.now() - start
      
      if (process.env.NODE_ENV === 'development') {
        console.error(`❌ Async operation "${operationName}" failed after ${duration.toFixed(2)}ms`, error)
      }
      
      throw error
    }
  }, [])

  // Get current metrics
  const getMetrics = useCallback((): Partial<PerformanceMetrics> => {
    return { ...metricsRef.current }
  }, [])

  return {
    trackInteraction,
    measureAsync,
    getMetrics,
  }
}

// Hook for tracking page performance
export function usePagePerformance(pageName: string) {
  useEffect(() => {
    // Track page load performance using Navigation Timing API
    if (typeof window !== 'undefined' && 'performance' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming
            
            const metrics = {
              dns: navEntry.domainLookupEnd - navEntry.domainLookupStart,
              tcp: navEntry.connectEnd - navEntry.connectStart,
              request: navEntry.responseStart - navEntry.requestStart,
              response: navEntry.responseEnd - navEntry.responseStart,
              dom: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
              load: navEntry.loadEventEnd - navEntry.loadEventStart,
              total: navEntry.loadEventEnd - navEntry.navigationStart,
            }
            
            if (process.env.NODE_ENV === 'development') {
              console.group(`📊 Page Performance: ${pageName}`)
              console.log(`DNS Lookup: ${metrics.dns.toFixed(2)}ms`)
              console.log(`TCP Connection: ${metrics.tcp.toFixed(2)}ms`)
              console.log(`Request: ${metrics.request.toFixed(2)}ms`)
              console.log(`Response: ${metrics.response.toFixed(2)}ms`)
              console.log(`DOM Processing: ${metrics.dom.toFixed(2)}ms`)
              console.log(`Load Event: ${metrics.load.toFixed(2)}ms`)
              console.log(`Total Load Time: ${metrics.total.toFixed(2)}ms`)
              console.groupEnd()
            }
          }
        })
      })
      
      observer.observe({ entryTypes: ['navigation'] })
      
      return () => observer.disconnect()
    }
  }, [pageName])
}

// Hook for tracking Core Web Vitals
export function useCoreWebVitals() {
  useEffect(() => {
    if (typeof window === 'undefined') return

    // Track Largest Contentful Paint (LCP)
    const observeLCP = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        
        if (process.env.NODE_ENV === 'development') {
          console.log(`🎯 LCP: ${lastEntry.startTime.toFixed(2)}ms`)
        }
      })
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
      return observer
    }

    // Track First Input Delay (FID)
    const observeFID = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        
        entries.forEach((entry) => {
          const fid = entry.processingStart - entry.startTime
          
          if (process.env.NODE_ENV === 'development') {
            console.log(`⚡ FID: ${fid.toFixed(2)}ms`)
          }
        })
      })
      
      observer.observe({ entryTypes: ['first-input'] })
      return observer
    }

    // Track Cumulative Layout Shift (CLS)
    const observeCLS = () => {
      let clsValue = 0
      
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        
        if (process.env.NODE_ENV === 'development') {
          console.log(`📐 CLS: ${clsValue.toFixed(4)}`)
        }
      })
      
      observer.observe({ entryTypes: ['layout-shift'] })
      return observer
    }

    const observers = [
      observeLCP(),
      observeFID(),
      observeCLS(),
    ]

    return () => {
      observers.forEach(observer => observer.disconnect())
    }
  }, [])
}

// Utility function to measure function execution time
export function measureExecutionTime<T extends (...args: any[]) => any>(
  fn: T,
  name?: string
): T {
  return ((...args: Parameters<T>) => {
    const start = performance.now()
    const result = fn(...args)
    const end = performance.now()
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`⏱️ Function "${name || fn.name}" executed in ${(end - start).toFixed(2)}ms`)
    }
    
    return result
  }) as T
}
