"use client"

import { useState, useMemo } from "react"
import ProductCard from "../components/ProductCard"
import Pagination from "../components/Pagination"
import { useData } from "../contexts/DataContext"

export default function ShopPage() {
  const [currentPage, setCurrentPage] = useState(1)
  const productsPerPage = 20

  // Use centralized data context
  const { products, currentUser } = useData()
  const userRole = currentUser?.role || "user"

  // Sort products by popularity
  const sortedProducts = useMemo(() => {
    return [...products].sort((a, b) => b.commentCount - a.commentCount)
  }, [products])

  // Pagination logic
  const { paginatedProducts, totalPages } = useMemo(() => {
    const startIndex = (currentPage - 1) * productsPerPage
    const endIndex = startIndex + productsPerPage
    const paginatedProducts = sortedProducts.slice(startIndex, endIndex)
    const totalPages = Math.ceil(sortedProducts.length / productsPerPage)

    return { paginatedProducts, totalPages }
  }, [sortedProducts, currentPage, productsPerPage])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Products Grid - 3 columns on mobile */}
      <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 md:gap-4 mb-8">
        {paginatedProducts.map((product) => (
          <ProductCard key={product.id} product={product} userRole={userRole} />
        ))}
      </div>

      {/* Pagination - Always show for testing */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        className="mt-8"
      />
    </div>
  )
}
