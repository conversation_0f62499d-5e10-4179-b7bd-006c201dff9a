/**
 * Currency utility functions for the Bentakon store
 * Handles currency formatting and conversion
 */

// Exchange rate from EUR to USD (this should be fetched from an API in production)
const EUR_TO_USD_RATE = 1.08

/**
 * Convert EUR to USD
 */
export function convertEurToUsd(eurAmount: number): number {
  return eurAmount * EUR_TO_USD_RATE
}

/**
 * Format price in USD
 */
export function formatUSD(amount: number): string {
  return `$${amount.toFixed(2)}`
}

/**
 * Format price with currency symbol
 */
export function formatPrice(amount: number, currency: 'USD' | 'EUR' = 'USD'): string {
  if (currency === 'USD') {
    return formatUSD(amount)
  } else {
    return `€${amount.toFixed(2)}`
  }
}

/**
 * Convert EUR price to USD and format
 */
export function convertAndFormatPrice(eurAmount: number): string {
  const usdAmount = convertEurToUsd(eurAmount)
  return formatUSD(usdAmount)
}

/**
 * Get currency symbol
 */
export function getCurrencySymbol(currency: 'USD' | 'EUR' = 'USD'): string {
  return currency === 'USD' ? '$' : '€'
}
