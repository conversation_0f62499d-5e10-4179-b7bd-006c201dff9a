import Image from "next/image"
import Link from "next/link"
import { Star } from "lucide-react"
import type { Product } from "../types"

interface ProductCardProps {
  product: Product
  userRole?: "admin" | "distributor" | "user"
}

export default function ProductCard({ product, userRole = "user" }: ProductCardProps) {
  const cheapestPackage = product.packages.reduce(
    (min, pkg) => (pkg.price < min.price ? pkg : min),
    product.packages[0],
  )

  return (
    <Link href={`/product/${product.slug}`}>
      <div className="bg-gray-800 rounded-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:scale-[1.02] border border-gray-700/30 h-full flex flex-col group">
        {/* Product Image - Square aspect ratio (1:1) */}
        <div className="relative aspect-square overflow-hidden">
          <Image
            src={product.coverImage || "/logo.jpg"}
            alt={product.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
          />

          {/* Discount Badge - Top Left (matching reference) */}
          {cheapestPackage.discount && (
            <div className="absolute top-2 left-2 z-10">
              <div className="bg-gradient-to-r from-pink-500 to-purple-500 text-white px-2 py-1 rounded text-xs font-bold shadow-lg">
                {cheapestPackage.discount}%OFF
              </div>
            </div>
          )}

          {/* Bottom gradient overlay */}
          <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
        </div>

        {/* Product Info - 2:1 aspect ratio (twice as wide as tall) */}
        <div className="aspect-[2/1] p-2 flex flex-col justify-between bg-gray-800">
          {/* Product Title */}
          <h3 className="text-xs font-semibold text-white mb-1 line-clamp-2 leading-tight">
            {product.title}
          </h3>

          <div className="space-y-1">
            {/* Rating and Reviews */}
            <div className="flex items-center space-x-1 space-x-reverse">
              <span className="text-yellow-400 font-bold text-xs">{product.rating}</span>
              {/* 5 Stars */}
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className="w-2.5 h-2.5 text-yellow-400 fill-current"
                />
              ))}
            </div>

            {/* Review Count */}
            <div className="text-gray-400 text-xs">
              {product.commentCount.toLocaleString()} تقييم
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}
