@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262.1 83.3% 57.8%;

    /* Modern Navbar Custom Properties */
    --navbar-height: 4rem;
    --navbar-blur: 20px;
    --navbar-bg-primary: rgba(17, 24, 39, 0.85);
    --navbar-bg-secondary: rgba(31, 41, 55, 0.75);
    --navbar-border: rgba(75, 85, 99, 0.2);
    --navbar-border-hover: rgba(139, 92, 246, 0.3);
    --navbar-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    --navbar-shadow-hover: 0 12px 40px rgba(139, 92, 246, 0.15);

    /* Animation Timing */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
    --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
    --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Glassmorphism Properties */
    --glass-bg-light: rgba(255, 255, 255, 0.05);
    --glass-bg-medium: rgba(255, 255, 255, 0.08);
    --glass-bg-strong: rgba(255, 255, 255, 0.12);
    --glass-border-light: rgba(255, 255, 255, 0.1);
    --glass-border-medium: rgba(255, 255, 255, 0.15);
    --glass-shadow-light: 0 4px 16px rgba(0, 0, 0, 0.1);
    --glass-shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.15);
    --glass-shadow-strong: 0 16px 64px rgba(0, 0, 0, 0.2);
  }
}

@layer components {
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .card-hover {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-purple-500/20;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-2 px-4 rounded-xl transition-all duration-300 shadow-lg hover:shadow-purple-500/25 active:scale-95;
  }

  .btn-secondary {
    @apply bg-gray-700/50 backdrop-blur-sm hover:bg-gray-600/50 text-white font-semibold py-2 px-4 rounded-xl transition-all duration-300 border border-gray-600/50 active:scale-95;
  }

  .glass-effect {
    @apply bg-gray-800/50 backdrop-blur-md border border-gray-700/50;
  }

  /* Mobile-Responsive Utilities */
  .mobile-card {
    @apply bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50 transition-all duration-300 hover:border-gray-500/50;
  }

  .mobile-button {
    @apply flex items-center justify-center space-x-2 space-x-reverse px-4 py-3 rounded-xl transition-all duration-300 font-medium text-sm active:scale-95;
  }

  .mobile-grid-actions {
    @apply grid grid-cols-2 gap-2 pt-3 border-t border-gray-600/50;
  }

  .mobile-grid-actions-3 {
    @apply grid grid-cols-3 gap-2 pt-3 border-t border-gray-600/50;
  }

  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* Simplified Modern Navbar Classes */
  .navbar-modern {
    background: rgba(17, 24, 39, 0.95);
    backdrop-filter: blur(16px);
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
  }

  .nav-link-modern {
    @apply relative px-3 py-2 rounded-xl font-medium text-gray-200 hover:text-white transition-colors duration-200;
  }

  .nav-link-modern::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #8b5cf6, #3b82f6);
    border-radius: 1px;
    transform: translateX(-50%);
    transition: width 0.3s ease;
  }

  .nav-link-modern:hover::after {
    width: 70%;
  }

  .glass-element {
    background: rgba(31, 41, 55, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(75, 85, 99, 0.3);
    transition: all 0.2s ease;
  }

  .glass-element:hover {
    background: rgba(31, 41, 55, 0.9);
    border-color: rgba(139, 92, 246, 0.4);
  }

  /* Enhanced Mobile Glass Effects */
  .glass-mobile {
    background: rgba(31, 41, 55, 0.85);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(75, 85, 99, 0.4);
    transition: all 0.3s ease;
  }

  .glass-mobile:active {
    background: rgba(31, 41, 55, 0.95);
    transform: scale(0.98);
  }

  /* Mobile-First Responsive Design Classes */
  .responsive-container {
    @apply container mx-auto px-4 py-4 md:py-8;
  }

  .responsive-header {
    @apply flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 md:mb-8;
  }

  .responsive-title {
    @apply text-xl md:text-2xl lg:text-3xl font-bold;
  }

  .responsive-grid-cards {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6;
  }

  .responsive-grid-stats {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-6;
  }

  .responsive-modal {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-2 md:p-4;
  }

  .responsive-modal-content {
    @apply bg-gray-800/90 backdrop-blur-md rounded-xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/50 shadow-2xl;
  }

  .responsive-modal-header {
    @apply p-4 md:p-6 border-b border-gray-700/50;
  }

  .responsive-modal-body {
    @apply p-4 md:p-6 space-y-4 md:space-y-6;
  }

  /* Simplified Animation Classes */
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modern Animation Keyframes */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes staggerFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.7);
}

/* Enhanced Glassmorphism Effects */
.glass-card {
  background: rgba(31, 41, 55, 0.5);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-card:hover {
  background: rgba(31, 41, 55, 0.6);
  border-color: rgba(139, 92, 246, 0.3);
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.1);
}

/* Simplified Glassmorphism Effects */
.glass-navbar {
  background: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(16px);
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.glass-button {
  background: rgba(31, 41, 55, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  transition: all 0.2s ease;
}

.glass-button:hover {
  background: rgba(31, 41, 55, 0.9);
  border-color: rgba(139, 92, 246, 0.4);
}

.glass-menu {
  background: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(75, 85, 99, 0.4);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Responsive Design Utilities */
@media (max-width: 1023px) {
  .navbar-modern {
    padding: 0 1rem;
  }

  .nav-link-modern {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }
}

@media (max-width: 767px) {
  .navbar-modern {
    height: 3.5rem;
    padding: 0 0.75rem;
  }

  .glass-menu {
    margin: 0.5rem;
    padding: 1rem;
    border-radius: 1rem;
  }

  .nav-link-modern {
    font-size: 1rem;
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 475px) {
  .navbar-modern {
    height: 3rem;
    padding: 0 0.5rem;
  }

  .glass-menu {
    margin: 0.25rem;
    padding: 0.75rem;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .nav-link-modern:hover::before {
    opacity: 0;
  }

  .nav-link-modern:active::before {
    opacity: 1;
  }

  .glass-button:hover {
    transform: none;
  }

  .glass-button:active {
    transform: scale(0.95);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-navbar {
    background: rgba(0, 0, 0, 0.9);
    border-bottom-color: rgba(255, 255, 255, 0.5);
  }

  .glass-button {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-float,
  .animate-glow,
  .animate-shimmer {
    animation: none;
  }

  .nav-link-modern,
  .glass-button,
  .navbar-modern {
    transition: none;
  }
}

/* Simplified Interaction Patterns */
/* Focus States for Accessibility */
.nav-link-modern:focus-visible,
.glass-button:focus-visible,
button:focus-visible {
  outline: 2px solid rgba(139, 92, 246, 0.8);
  outline-offset: 2px;
  border-radius: 0.75rem;
}
