"use client"

import Link from "next/link"
import Image from "next/image"
import { X, Home, Gamepad2, BookOpen, User, Wallet, Settings } from "lucide-react"
import { useEffect } from "react"
import { useData } from "../contexts/DataContext"

interface MobileSidebarProps {
  isOpen: boolean
  onClose: () => void
}

export default function MobileSidebar({ isOpen, onClose }: MobileSidebarProps) {
  // Use centralized data context
  const { currentUser } = useData()

  // Fallback user data if no current user (for development)
  const user = currentUser || {
    name: "مستخدم ضيف",
    walletBalance: 0,
    role: "user" as const,
  }

  // Close sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpen && !(event.target as Element).closest('.sidebar-container')) {
        onClose()
      }
    }
    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [isOpen, onClose])

  // Prevent body scroll when sidebar is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const navItems = [
    { href: "/", label: "الرئيسية", icon: Home },
    { href: "/shop", label: "الألعاب", icon: Gamepad2 },
    { href: "/blog", label: "المدونة", icon: BookOpen },
    { href: "/profile", label: "الملف الشخصي", icon: User },
    { href: "/wallet", label: "المحفظة", icon: Wallet },
    // Show admin dashboard only for admin users
    ...(user.role === "admin" ? [{ href: "/admin", label: "لوحة التحكم", icon: Settings }] : []),
  ]

  return (
    <>
      {/* Backdrop */}
      <div
        className={`fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300 lg:hidden ${
          isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={onClose}
      />

      {/* Sidebar */}
      <div
        className={`fixed top-0 left-0 h-full w-80 bg-gray-900/95 backdrop-blur-md border-r border-gray-800/50 z-50 transform transition-transform duration-300 ease-in-out lg:hidden sidebar-container ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-800/50">
          <Link href="/" className="flex items-center space-x-3 space-x-reverse" onClick={onClose}>
            <div className="w-10 h-10 rounded-lg overflow-hidden shadow-md">
              <Image
                src="/logo.jpg"
                alt="بنتاكون"
                width={40}
                height={40}
                className="w-full h-full object-cover"
              />
            </div>
            <span className="text-xl font-bold text-white">بنتاكون</span>
          </Link>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200"
          >
            <X className="w-6 h-6 text-gray-300" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="p-6">
          <div className="space-y-2">
            {navItems.map((item) => {
              const Icon = item.icon
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className="flex items-center space-x-3 space-x-reverse p-3 rounded-lg text-gray-200 hover:text-white hover:bg-gray-800/50 transition-all duration-200 group"
                  onClick={onClose}
                >
                  <Icon className="w-5 h-5 text-gray-400 group-hover:text-purple-400 transition-colors duration-200" />
                  <span className="font-medium">{item.label}</span>
                </Link>
              )
            })}
          </div>
        </nav>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-gray-800/50">
          <div className="text-center">
            <p className="text-sm text-gray-400 mb-3">مرحباً بك في بنتاكون</p>
            <button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-3 px-4 rounded-lg font-medium transition-all duration-200 hover:shadow-lg">
              تسجيل الدخول
            </button>
          </div>
        </div>
      </div>
    </>
  )
}
