# Supabase Integration TODO List

## Overview
This document outlines the complete integration plan for migrating from mock data to Supabase backend for the Bentakon Store project.

## 1. Database Schema Setup

### 1.1 Core Tables Creation
- [ ] **products** table
  - [ ] Create table with all fields from Product interface
  - [ ] Set up UUID primary key with auto-generation
  - [ ] Create unique index on `slug` field
  - [ ] Create indexes on `category`, `featured`, `popular` fields
  - [ ] Add full-text search index on `title` and `description`

- [ ] **packages** table
  - [ ] Create table with Package interface fields
  - [ ] Set up foreign key relationship to products table
  - [ ] Create index on `product_id` and `has_digital_codes`
  - [ ] Add computed column for `available_codes_count`

- [ ] **digital_codes** table
  - [ ] Create table with DigitalCode interface fields
  - [ ] Set up foreign key relationship to packages table
  - [ ] Create indexes on `package_id`, `used`, `assigned_to_order_id`
  - [ ] Implement encryption for `key` field
  - [ ] Add audit trail columns (created_at, updated_at)

- [ ] **custom_fields** table
  - [ ] Create table for product custom fields
  - [ ] Set up foreign key relationship to products table
  - [ ] Create index on `product_id` and `required`

- [ ] **dropdowns** table
  - [ ] Create table for product dropdown fields
  - [ ] Set up foreign key relationship to products table

- [ ] **dropdown_options** table
  - [ ] Create table for dropdown options
  - [ ] Set up foreign key relationship to dropdowns table

### 1.2 User Management Tables
- [ ] **user_profiles** table (extends Supabase Auth users)
  - [ ] Create profile table linked to auth.users
  - [ ] Add fields: name, role, wallet_balance, avatar
  - [ ] Set up RLS policies for user data protection
  - [ ] Create indexes on `role` field

### 1.3 Order Management Tables
- [ ] **orders** table
  - [ ] Create table with Order interface fields
  - [ ] Set up foreign keys to users, products, packages
  - [ ] Create indexes on `user_id`, `status`, `created_at`
  - [ ] Add triggers for order status updates

### 1.4 Homepage Configuration Tables
- [ ] **banner_slides** table
  - [ ] Create table for homepage banners
  - [ ] Add indexes on `active` and `order` fields
  - [ ] Set up ordering constraints

- [ ] **homepage_sections** table
  - [ ] Create table for homepage sections
  - [ ] Handle `product_ids` as JSON array or separate junction table
  - [ ] Add indexes on `active` and `order` fields

## 2. Row Level Security (RLS) Policies

### 2.1 User Access Policies
- [ ] **users table**: Users can only read/update their own profile
- [ ] **orders table**: Users can only access their own orders
- [ ] **digital_codes**: Users can only view codes assigned to their orders

### 2.2 Admin Access Policies
- [ ] **products**: Admins have full CRUD access
- [ ] **packages**: Admins have full CRUD access
- [ ] **digital_codes**: Admins have full CRUD access
- [ ] **users**: Admins can read all users, update roles
- [ ] **orders**: Admins can read/update all orders
- [ ] **homepage config**: Admins have full CRUD access

### 2.3 Public Access Policies
- [ ] **products**: Public read access for active products
- [ ] **packages**: Public read access for active packages
- [ ] **homepage config**: Public read access for active banners/sections

## 3. Database Functions and Triggers

### 3.1 Business Logic Functions
- [ ] **assign_digital_code()**: Automatically assign available code to order
- [ ] **update_wallet_balance()**: Handle wallet transactions
- [ ] **calculate_package_discount()**: Compute discount percentages
- [ ] **get_available_codes_count()**: Count available codes per package

### 3.2 Audit and Logging Triggers
- [ ] **order_status_change_log**: Log all order status changes
- [ ] **digital_code_view_log**: Track when codes are viewed
- [ ] **user_activity_log**: Track important user actions

## 4. API Integration Layer

### 4.1 Supabase Client Setup
- [ ] Install and configure Supabase client
- [ ] Set up environment variables for Supabase URL and keys
- [ ] Configure client for server-side and client-side usage

### 4.2 Data Access Layer
- [ ] Create service classes for each entity:
  - [ ] ProductService
  - [ ] PackageService
  - [ ] DigitalCodeService
  - [ ] UserService
  - [ ] OrderService
  - [ ] HomepageService

### 4.3 Real-time Subscriptions
- [ ] Set up real-time listeners for:
  - [ ] Order status changes
  - [ ] Digital code assignments
  - [ ] Homepage configuration updates
  - [ ] Product inventory changes

## 5. Authentication Integration

### 5.1 Supabase Auth Setup
- [ ] Configure Supabase Auth providers (email, social)
- [ ] Set up email templates for auth flows
- [ ] Configure redirect URLs for production/development

### 5.2 Role-Based Access Control
- [ ] Implement role checking middleware
- [ ] Create admin route protection
- [ ] Set up distributor role permissions
- [ ] Handle role-based UI rendering

### 5.3 Session Management
- [ ] Replace mock user data with Supabase session
- [ ] Implement proper login/logout flows
- [ ] Handle session persistence and refresh

## 6. Data Migration

### 6.1 Mock Data Migration
- [ ] Create migration scripts for existing mock data
- [ ] Migrate products and packages
- [ ] Migrate digital codes with proper encryption
- [ ] Migrate homepage configuration

### 6.2 Data Validation
- [ ] Validate all migrated data integrity
- [ ] Test foreign key relationships
- [ ] Verify RLS policies work correctly

## 7. Security Implementation

### 7.1 Data Encryption
- [ ] Implement encryption for digital codes
- [ ] Set up secure key management
- [ ] Encrypt sensitive user data

### 7.2 API Security
- [ ] Implement rate limiting
- [ ] Add request validation middleware
- [ ] Set up CORS policies
- [ ] Implement API key management for admin functions

## 8. Performance Optimization

### 8.1 Database Optimization
- [ ] Optimize queries with proper indexes
- [ ] Implement database connection pooling
- [ ] Set up query performance monitoring

### 8.2 Caching Strategy
- [ ] Implement Redis caching for frequently accessed data
- [ ] Cache product listings and homepage data
- [ ] Set up cache invalidation strategies

## 9. Testing and Validation

### 9.1 Unit Tests
- [ ] Write tests for all service classes
- [ ] Test RLS policies
- [ ] Test database functions and triggers

### 9.2 Integration Tests
- [ ] Test complete user flows
- [ ] Test admin dashboard functionality
- [ ] Test real-time updates

## 10. Deployment and Monitoring

### 10.1 Environment Setup
- [ ] Set up production Supabase project
- [ ] Configure environment-specific settings
- [ ] Set up database backups

### 10.2 Monitoring
- [ ] Set up error tracking
- [ ] Monitor database performance
- [ ] Set up alerts for critical issues

## Priority Order
1. **High Priority**: Database schema, RLS policies, authentication
2. **Medium Priority**: API integration, data migration, basic security
3. **Low Priority**: Performance optimization, advanced monitoring

## Estimated Timeline
- **Phase 1** (Database & Auth): 2-3 weeks
- **Phase 2** (API Integration): 2-3 weeks  
- **Phase 3** (Security & Testing): 1-2 weeks
- **Phase 4** (Optimization & Deployment): 1 week

**Total Estimated Time**: 6-9 weeks
