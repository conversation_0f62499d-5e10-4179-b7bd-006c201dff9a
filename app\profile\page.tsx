"use client"

import { useState } from "react"
import { User, Mail, Lock, Edit, Save, X } from "lucide-react"

export default function ProfilePage() {
  // TODO: Replace with actual user data from Supabase auth
  const [user, setUser] = useState({
    id: "3",
    name: "أحمد محمد",
    email: "<EMAIL>",
    walletBalance: 150.0,
    joinDate: "2024-01-01",
  })

  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    name: user.name,
    email: user.email,
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleSave = async () => {
    setIsLoading(true)

    // TODO: Implement user update with Supabase
    // Validate passwords match
    if (editForm.newPassword && editForm.newPassword !== editForm.confirmPassword) {
      alert("كلمات المرور غير متطابقة")
      setIsLoading(false)
      return
    }

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Update user data
    setUser((prev) => ({
      ...prev,
      name: editForm.name,
      email: editForm.email,
    }))

    setIsEditing(false)
    setEditForm((prev) => ({
      ...prev,
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    }))
    setIsLoading(false)
    alert("تم تحديث الملف الشخصي بنجاح!")
  }

  const handleCancel = () => {
    setEditForm({
      name: user.name,
      email: user.email,
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    })
    setIsEditing(false)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
          الملف الشخصي
        </h1>
        <p className="text-gray-400 text-lg">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
      </div>

      <div className="max-w-2xl mx-auto">
        {/* Profile Card */}
        <div className="bg-gray-800 rounded-xl border border-gray-700 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                  <User className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">{user.name}</h2>
                  <p className="text-purple-100">عضو منذ {new Date(user.joinDate).getFullYear()}</p>
                </div>
              </div>
              {!isEditing && (
                <button
                  onClick={() => setIsEditing(true)}
                  className="bg-white/20 hover:bg-white/30 text-white p-2 rounded-lg transition-colors"
                >
                  <Edit className="w-5 h-5" />
                </button>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {!isEditing ? (
              /* View Mode */
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-gray-700/50 rounded-lg p-4">
                    <div className="flex items-center space-x-3 space-x-reverse mb-2">
                      <User className="w-5 h-5 text-purple-400" />
                      <span className="text-sm text-gray-400">الاسم</span>
                    </div>
                    <p className="text-lg font-semibold">{user.name}</p>
                  </div>

                  <div className="bg-gray-700/50 rounded-lg p-4">
                    <div className="flex items-center space-x-3 space-x-reverse mb-2">
                      <Mail className="w-5 h-5 text-blue-400" />
                      <span className="text-sm text-gray-400">البريد الإلكتروني</span>
                    </div>
                    <p className="text-lg font-semibold">{user.email}</p>
                  </div>
                </div>

                <div className="bg-gray-700/50 rounded-lg p-4">
                  <div className="flex items-center space-x-3 space-x-reverse mb-2">
                    <Lock className="w-5 h-5 text-green-400" />
                    <span className="text-sm text-gray-400">كلمة المرور</span>
                  </div>
                  <p className="text-lg">••••••••</p>
                </div>

                <div className="bg-gradient-to-r from-green-600/20 to-blue-600/20 rounded-lg p-4 border border-green-500/30">
                  <h3 className="text-lg font-semibold mb-2">رصيد المحفظة</h3>
                  <p className="text-2xl font-bold text-green-400">${user.walletBalance.toFixed(2)}</p>
                </div>
              </div>
            ) : (
              /* Edit Mode */
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">الاسم</label>
                    <input
                      type="text"
                      value={editForm.name}
                      onChange={(e) => setEditForm((prev) => ({ ...prev, name: e.target.value }))}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">البريد الإلكتروني</label>
                    <input
                      type="email"
                      value={editForm.email}
                      onChange={(e) => setEditForm((prev) => ({ ...prev, email: e.target.value }))}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500"
                    />
                  </div>
                </div>

                <div className="border-t border-gray-700 pt-6">
                  <h3 className="text-lg font-semibold mb-4">تغيير كلمة المرور</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">كلمة المرور الحالية</label>
                      <input
                        type="password"
                        value={editForm.currentPassword}
                        onChange={(e) => setEditForm((prev) => ({ ...prev, currentPassword: e.target.value }))}
                        className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500"
                        placeholder="أدخل كلمة المرور الحالية"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">كلمة المرور الجديدة</label>
                        <input
                          type="password"
                          value={editForm.newPassword}
                          onChange={(e) => setEditForm((prev) => ({ ...prev, newPassword: e.target.value }))}
                          className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500"
                          placeholder="كلمة المرور الجديدة"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">تأكيد كلمة المرور</label>
                        <input
                          type="password"
                          value={editForm.confirmPassword}
                          onChange={(e) => setEditForm((prev) => ({ ...prev, confirmPassword: e.target.value }))}
                          className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500"
                          placeholder="تأكيد كلمة المرور الجديدة"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4 space-x-reverse pt-6 border-t border-gray-700">
                  <button
                    onClick={handleSave}
                    disabled={isLoading}
                    className="flex-1 btn-primary flex items-center justify-center space-x-2 space-x-reverse"
                  >
                    {isLoading ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    ) : (
                      <>
                        <Save className="w-5 h-5" />
                        <span>حفظ التغييرات</span>
                      </>
                    )}
                  </button>
                  <button
                    onClick={handleCancel}
                    disabled={isLoading}
                    className="flex-1 btn-secondary flex items-center justify-center space-x-2 space-x-reverse"
                  >
                    <X className="w-5 h-5" />
                    <span>إلغاء</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
