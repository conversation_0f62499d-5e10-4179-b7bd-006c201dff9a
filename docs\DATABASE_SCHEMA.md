# Database Schema Documentation

## Overview
This document provides a comprehensive overview of the Bentakon Store database schema, including table structures, relationships, and security considerations.

## Entity Relationship Diagram

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   users     │    │  products   │    │  packages   │
│             │    │             │    │             │
│ id (PK)     │    │ id (PK)     │    │ id (PK)     │
│ email       │    │ slug (UK)   │    │ product_id  │
│ name        │    │ title       │    │ name        │
│ role        │    │ description │    │ price       │
│ wallet_bal  │    │ category    │    │ ...         │
│ avatar      │    │ ...         │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       │                   │                   │
       │                   └───────────────────┘
       │                                       │
       │            ┌─────────────┐           │
       │            │   orders    │           │
       │            │             │           │
       │            │ id (PK)     │           │
       └────────────│ user_id     │           │
                    │ product_id  │───────────┘
                    │ package_id  │───────────┘
                    │ amount      │
                    │ status      │
                    │ created_at  │
                    └─────────────┘
                           │
                           │
                    ┌─────────────┐
                    │digital_codes│
                    │             │
                    │ id (PK)     │
                    │ package_id  │
                    │ key         │
                    │ used        │
                    │ assigned_to │
                    └─────────────┘
```

## Table Definitions

### 1. users (Supabase Auth Integration)
**Purpose**: Store user profiles and authentication data
**Type**: Extends Supabase auth.users table

```sql
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  name TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('admin', 'distributor', 'user')),
  wallet_balance DECIMAL(10,2) DEFAULT 0.00,
  avatar TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_user_profiles_role ON user_profiles(role);

-- RLS Policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can view all profiles" ON user_profiles FOR ALL USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);
```

### 2. products
**Purpose**: Store game and digital service information
**Relationships**: One-to-Many with packages, custom_fields, dropdowns

```sql
CREATE TABLE products (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  slug TEXT UNIQUE NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  cover_image TEXT NOT NULL,
  category TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  rating DECIMAL(2,1) DEFAULT 0.0 CHECK (rating >= 0 AND rating <= 5),
  comment_count INTEGER DEFAULT 0,
  featured BOOLEAN DEFAULT FALSE,
  popular BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE UNIQUE INDEX idx_products_slug ON products(slug);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_featured ON products(featured);
CREATE INDEX idx_products_popular ON products(popular);
CREATE INDEX idx_products_search ON products USING gin(to_tsvector('english', title || ' ' || description));

-- RLS Policies
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Products are viewable by everyone" ON products FOR SELECT USING (true);
CREATE POLICY "Only admins can modify products" ON products FOR ALL USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);
```

### 3. packages
**Purpose**: Store different purchase options for products
**Relationships**: Many-to-One with products, One-to-Many with digital_codes

```sql
CREATE TABLE packages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
  original_price DECIMAL(10,2) CHECK (original_price >= price),
  discount INTEGER GENERATED ALWAYS AS (
    CASE 
      WHEN original_price IS NOT NULL AND original_price > 0 
      THEN ROUND(((original_price - price) / original_price * 100)::numeric, 0)::integer
      ELSE 0 
    END
  ) STORED,
  image TEXT NOT NULL,
  description TEXT,
  has_digital_codes BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_packages_product_id ON packages(product_id);
CREATE INDEX idx_packages_has_digital_codes ON packages(has_digital_codes);

-- RLS Policies
ALTER TABLE packages ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Packages are viewable by everyone" ON packages FOR SELECT USING (true);
CREATE POLICY "Only admins can modify packages" ON packages FOR ALL USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);
```

### 4. digital_codes
**Purpose**: Store redeemable codes for digital products
**Relationships**: Many-to-One with packages, One-to-One with orders
**Security**: Keys are encrypted at rest

```sql
CREATE TABLE digital_codes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  package_id UUID REFERENCES packages(id) ON DELETE CASCADE,
  key_encrypted TEXT NOT NULL, -- Encrypted digital code
  used BOOLEAN DEFAULT FALSE,
  assigned_to_order_id UUID REFERENCES orders(id),
  assigned_at TIMESTAMP WITH TIME ZONE,
  viewed_count INTEGER DEFAULT 0,
  last_viewed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_digital_codes_package_id ON digital_codes(package_id);
CREATE INDEX idx_digital_codes_used ON digital_codes(used);
CREATE INDEX idx_digital_codes_assigned_to_order_id ON digital_codes(assigned_to_order_id);

-- RLS Policies
ALTER TABLE digital_codes ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view codes assigned to their orders" ON digital_codes FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM orders 
    WHERE orders.id = digital_codes.assigned_to_order_id 
    AND orders.user_id = auth.uid()
  )
);
CREATE POLICY "Admins can manage all digital codes" ON digital_codes FOR ALL USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);
```

### 5. orders
**Purpose**: Store customer purchase transactions
**Relationships**: Many-to-One with users, products, packages

```sql
CREATE TABLE orders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id),
  package_id UUID REFERENCES packages(id),
  amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
  status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed')),
  custom_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_product_id ON orders(product_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);

-- RLS Policies
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own orders" ON orders FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Users can create own orders" ON orders FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "Admins can view all orders" ON orders FOR ALL USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);
```

## Security Considerations

### 1. Row Level Security (RLS)
- All tables have RLS enabled
- Users can only access their own data
- Admins have elevated permissions
- Public data (products, packages) is readable by everyone

### 2. Data Encryption
- Digital codes are encrypted using Supabase's built-in encryption
- Sensitive user data is protected
- API keys and secrets are stored securely

### 3. Audit Trail
- All tables include created_at and updated_at timestamps
- Order status changes are logged
- Digital code access is tracked

## Performance Optimizations

### 1. Indexing Strategy
- Primary keys on all tables
- Foreign key indexes for joins
- Search indexes for product discovery
- Composite indexes for common query patterns

### 2. Query Optimization
- Use of materialized views for complex aggregations
- Proper use of LIMIT and OFFSET for pagination
- Efficient joins using indexed columns

### 3. Caching Strategy
- Product catalog cached at application level
- Homepage configuration cached
- User sessions cached for performance

## Backup and Recovery

### 1. Automated Backups
- Daily automated backups via Supabase
- Point-in-time recovery available
- Cross-region backup replication

### 2. Data Retention
- Order data retained indefinitely for accounting
- User activity logs retained for 1 year
- Digital code access logs retained for 6 months
